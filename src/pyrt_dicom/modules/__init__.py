"""DICOM Modules - Concrete implementations of DICOM specification modules.

Each module represents a specific section of the DICOM standard, implemented as
concrete Python classes with all data elements as attributes for IntelliSense support.

All modules inherit from BaseModule for consistent API patterns and validation.
"""

# Patient Information Modules
from .patient_module import PatientModule
from .clinical_trial_subject_module import ClinicalTrialSubjectModule
from .patient_study_module import PatientStudyModule

# Study & Series Modules
from .general_study_module import GeneralStudyModule
from .clinical_trial_study_module import ClinicalTrialStudyModule
from .general_series_module import GeneralSeriesModule
from .rt_series_module import RTSeriesModule
from .clinical_trial_series_module import ClinicalTrialSeriesModule

# Image & Spatial Modules
from .general_image_module import GeneralImageModule
from .image_plane_module import ImagePlaneModule
from .image_pixel_module import ImagePixelModule
from .multi_frame_module import MultiFrameModule
from .frame_of_reference_module import FrameOfReferenceModule
from .cine_module import CineModule
from .overlay_plane_module import OverlayPlaneModule

# Equipment & Common Modules
from .general_equipment_module import GeneralEquipmentModule
from .sop_common_module import SOPCommonModule
from .common_instance_reference_module import CommonInstanceReferenceModule
from .device_module import DeviceModule
from .general_reference_module import GeneralReferenceModule

__all__ = [
    # Patient Information Modules
    "PatientModule",
    "ClinicalTrialSubjectModule",
    "PatientStudyModule",

    # Study & Series Modules
    "GeneralStudyModule",
    "ClinicalTrialStudyModule",
    "GeneralSeriesModule",
    "RTSeriesModule",
    "ClinicalTrialSeriesModule",

    # Image & Spatial Modules
    "GeneralImageModule",
    "ImagePlaneModule",
    "ImagePixelModule",
    "MultiFrameModule",
    "FrameOfReferenceModule",
    "CineModule",
    "OverlayPlaneModule",

    # Equipment & Common Modules
    "GeneralEquipmentModule",
    "SOPCommonModule",
    "CommonInstanceReferenceModule",
    "DeviceModule",
    "GeneralReferenceModule",
]
